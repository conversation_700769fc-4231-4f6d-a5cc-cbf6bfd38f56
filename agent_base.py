"""
Base Agent Classes for Agentic AI System
This module defines the core agent architecture and interfaces.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple
import time
import logging
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentState(Enum):
    """Possible states for an agent"""
    IDLE = "idle"
    THINKING = "thinking"
    ACTING = "acting"
    LEARNING = "learning"
    COMMUNICATING = "communicating"

@dataclass
class Perception:
    """Represents what an agent perceives from its environment"""
    timestamp: float
    data: Dict[str, Any]
    source: str
    confidence: float = 1.0

@dataclass
class Action:
    """Represents an action an agent can take"""
    action_type: str
    parameters: Dict[str, Any]
    timestamp: float
    expected_outcome: Optional[str] = None

@dataclass
class Goal:
    """Represents a goal for an agent"""
    description: str
    priority: int
    deadline: Optional[float] = None
    success_criteria: Dict[str, Any] = None
    completed: bool = False

class Environment(ABC):
    """Abstract base class for agent environments"""
    
    @abstractmethod
    def get_state(self) -> Dict[str, Any]:
        """Get current environment state"""
        pass
    
    @abstractmethod
    def execute_action(self, action: Action) -> Tuple[bool, Dict[str, Any]]:
        """Execute an action and return success status and results"""
        pass
    
    @abstractmethod
    def get_available_actions(self) -> List[str]:
        """Get list of available actions in current state"""
        pass

class Agent(ABC):
    """Abstract base class for all agents"""
    
    def __init__(self, agent_id: str, name: str):
        self.agent_id = agent_id
        self.name = name
        self.state = AgentState.IDLE
        self.goals: List[Goal] = []
        self.memory: List[Perception] = []
        self.knowledge_base: Dict[str, Any] = {}
        self.performance_metrics: Dict[str, float] = {}
        self.creation_time = time.time()
        
    @abstractmethod
    def perceive(self, environment: Environment) -> Perception:
        """Perceive the current environment state"""
        pass
    
    @abstractmethod
    def decide(self, perception: Perception) -> Optional[Action]:
        """Decide what action to take based on perception"""
        pass
    
    @abstractmethod
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """Learn from the result of an action"""
        pass
    
    def add_goal(self, goal: Goal) -> None:
        """Add a new goal to the agent"""
        self.goals.append(goal)
        logger.info(f"Agent {self.name} added goal: {goal.description}")
    
    def update_memory(self, perception: Perception) -> None:
        """Update agent's memory with new perception"""
        self.memory.append(perception)
        # Keep only recent memories (last 100)
        if len(self.memory) > 100:
            self.memory = self.memory[-100:]
    
    def get_active_goals(self) -> List[Goal]:
        """Get list of active (incomplete) goals"""
        return [goal for goal in self.goals if not goal.completed]
    
    def run_cycle(self, environment: Environment) -> Optional[Action]:
        """Run one complete agent cycle: perceive -> decide -> act"""
        try:
            # Perceive
            self.state = AgentState.THINKING
            perception = self.perceive(environment)
            self.update_memory(perception)
            
            # Decide
            action = self.decide(perception)
            
            if action:
                # Act
                self.state = AgentState.ACTING
                success, result = environment.execute_action(action)
                
                # Learn
                self.state = AgentState.LEARNING
                self.learn(action, result)
                
                logger.info(f"Agent {self.name} executed {action.action_type}: {'Success' if success else 'Failed'}")
                return action
            
        except Exception as e:
            logger.error(f"Error in agent {self.name} cycle: {e}")
        finally:
            self.state = AgentState.IDLE
            
        return None
