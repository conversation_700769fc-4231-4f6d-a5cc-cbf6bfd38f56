"""
Agentic AI Simulation
Demonstrates different types of agents working in a shared environment.
"""

import time
import random
from typing import List, Dict, Any
from agent_base import Goal, logger
from reactive_agent import ReactiveAgent
from goal_based_agent import GoalBasedAgent
from learning_agent import QLearningAgent
from multi_agent_system import CoordinatorAgent, CollaborativeAgent, CommunicationProtocol
from environment import GridWorld

class AgenticAISimulation:
    """
    Main simulation class that orchestrates different types of agents
    in a shared environment to demonstrate Agentic AI concepts.
    """
    
    def __init__(self, grid_size: int = 12):
        # Initialize environment
        self.environment = GridWorld(width=grid_size, height=grid_size)
        
        # Initialize communication system
        self.communication = CommunicationProtocol()
        
        # Initialize agents
        self.agents = {}
        self.agent_types = {}
        
        # Simulation parameters
        self.max_steps = 100
        self.current_step = 0
        self.simulation_stats = {
            "total_actions": 0,
            "successful_actions": 0,
            "goals_completed": 0,
            "messages_sent": 0
        }
        
        # Setup agents
        self._setup_agents()
        
    def _setup_agents(self) -> None:
        """Initialize different types of agents"""
        
        # 1. Reactive Agent - Simple stimulus-response behavior
        reactive_agent = ReactiveAgent("reactive_1", "ReactiveBot")
        self.agents["reactive_1"] = reactive_agent
        self.agent_types["reactive_1"] = "reactive"
        self.environment.add_agent("reactive_1")
        
        # 2. Goal-Based Agent - Plans actions to achieve goals
        goal_agent = GoalBasedAgent("goal_1", "PlannerBot")
        
        # Add some goals
        collection_goal = Goal(
            description="Collect 3 energy cells",
            priority=3,
            success_criteria={"items_collected": 3, "item_type": "energy_cell"}
        )
        exploration_goal = Goal(
            description="Explore the entire grid",
            priority=2,
            success_criteria={"areas_explored": 50}
        )
        
        goal_agent.add_goal(collection_goal)
        goal_agent.add_goal(exploration_goal)
        
        self.agents["goal_1"] = goal_agent
        self.agent_types["goal_1"] = "goal_based"
        self.environment.add_agent("goal_1")
        
        # 3. Learning Agent - Improves through experience
        learning_agent = QLearningAgent("learning_1", "LearnerBot", 
                                       learning_rate=0.1, epsilon=0.3)
        self.agents["learning_1"] = learning_agent
        self.agent_types["learning_1"] = "learning"
        self.environment.add_agent("learning_1")
        
        # 4. Coordinator Agent - Manages other agents
        coordinator = CoordinatorAgent("coordinator_1", "CoordinatorBot", self.communication)
        self.agents["coordinator_1"] = coordinator
        self.agent_types["coordinator_1"] = "coordinator"
        self.environment.add_agent("coordinator_1")
        
        # 5. Collaborative Agents - Work together
        collab_agent_1 = CollaborativeAgent("collab_1", "TeamBot1", self.communication)
        collab_agent_2 = CollaborativeAgent("collab_2", "TeamBot2", self.communication)
        
        # Set up collaboration relationships
        collab_agent_1.set_coordinator("coordinator_1")
        collab_agent_2.set_coordinator("coordinator_1")
        collab_agent_1.add_peer_agent("collab_2")
        collab_agent_2.add_peer_agent("collab_1")
        
        coordinator.add_managed_agent("collab_1")
        coordinator.add_managed_agent("collab_2")
        
        self.agents["collab_1"] = collab_agent_1
        self.agents["collab_2"] = collab_agent_2
        self.agent_types["collab_1"] = "collaborative"
        self.agent_types["collab_2"] = "collaborative"
        
        self.environment.add_agent("collab_1")
        self.environment.add_agent("collab_2")
        
        logger.info(f"Simulation initialized with {len(self.agents)} agents")
    
    def run_simulation(self, steps: int = None) -> Dict[str, Any]:
        """Run the simulation for specified number of steps"""
        if steps:
            self.max_steps = steps
        
        logger.info(f"Starting Agentic AI simulation for {self.max_steps} steps")
        
        start_time = time.time()
        
        for step in range(self.max_steps):
            self.current_step = step
            self._run_simulation_step()
            
            # Print progress every 20 steps
            if step % 20 == 0:
                self._print_progress()
        
        end_time = time.time()
        
        # Generate final report
        final_stats = self._generate_final_report(end_time - start_time)
        
        logger.info("Simulation completed!")
        return final_stats
    
    def _run_simulation_step(self) -> None:
        """Run one step of the simulation"""
        # Shuffle agent order for fairness
        agent_ids = list(self.agents.keys())
        random.shuffle(agent_ids)
        
        for agent_id in agent_ids:
            agent = self.agents[agent_id]
            
            try:
                # Get agent-specific environment state
                local_env = AgentEnvironmentWrapper(self.environment, agent_id)
                
                # Run agent cycle
                action = agent.run_cycle(local_env)
                
                if action:
                    self.simulation_stats["total_actions"] += 1
                    
                    # Execute action in environment
                    success, result = self.environment.execute_action(action)
                    
                    if success:
                        self.simulation_stats["successful_actions"] += 1
                    
                    # Check for goal completion
                    if result.get("goal_completed", False):
                        self.simulation_stats["goals_completed"] += 1
                
            except Exception as e:
                logger.error(f"Error in agent {agent_id} step: {e}")
        
        # Update communication stats
        self.simulation_stats["messages_sent"] = len(self.communication.message_history)
    
    def _print_progress(self) -> None:
        """Print simulation progress"""
        success_rate = (self.simulation_stats["successful_actions"] / 
                       max(1, self.simulation_stats["total_actions"]))
        
        print(f"\n--- Step {self.current_step} ---")
        print(f"Total Actions: {self.simulation_stats['total_actions']}")
        print(f"Success Rate: {success_rate:.2%}")
        print(f"Goals Completed: {self.simulation_stats['goals_completed']}")
        print(f"Messages Sent: {self.simulation_stats['messages_sent']}")
        
        # Print agent statuses
        print("\nAgent Status:")
        for agent_id, agent in self.agents.items():
            if hasattr(agent, 'get_status'):
                status = agent.get_status()
                print(f"  {agent_id} ({self.agent_types[agent_id]}): {status.get('state', 'unknown')}")
    
    def _generate_final_report(self, simulation_time: float) -> Dict[str, Any]:
        """Generate comprehensive simulation report"""
        
        # Calculate overall statistics
        total_actions = self.simulation_stats["total_actions"]
        success_rate = (self.simulation_stats["successful_actions"] / max(1, total_actions))
        
        # Collect agent-specific statistics
        agent_reports = {}
        for agent_id, agent in self.agents.items():
            if hasattr(agent, 'get_status'):
                agent_reports[agent_id] = {
                    "type": self.agent_types[agent_id],
                    "status": agent.get_status(),
                    "performance": getattr(agent, 'performance_metrics', {})
                }
        
        # Environment statistics
        env_stats = self.environment.get_environment_stats()
        
        # Communication statistics
        comm_stats = {
            "total_messages": len(self.communication.message_history),
            "message_types": {},
            "most_active_communicators": {}
        }
        
        # Analyze message types
        for message in self.communication.message_history:
            msg_type = message.message_type.value
            comm_stats["message_types"][msg_type] = comm_stats["message_types"].get(msg_type, 0) + 1
        
        # Find most active communicators
        sender_counts = {}
        for message in self.communication.message_history:
            sender_counts[message.sender_id] = sender_counts.get(message.sender_id, 0) + 1
        
        comm_stats["most_active_communicators"] = dict(sorted(sender_counts.items(), 
                                                             key=lambda x: x[1], reverse=True))
        
        final_report = {
            "simulation_summary": {
                "total_steps": self.current_step + 1,
                "simulation_time_seconds": simulation_time,
                "total_actions": total_actions,
                "successful_actions": self.simulation_stats["successful_actions"],
                "success_rate": success_rate,
                "goals_completed": self.simulation_stats["goals_completed"],
                "actions_per_second": total_actions / max(1, simulation_time)
            },
            "agent_reports": agent_reports,
            "environment_stats": env_stats,
            "communication_stats": comm_stats,
            "key_insights": self._generate_insights(agent_reports, success_rate)
        }
        
        return final_report
    
    def _generate_insights(self, agent_reports: Dict, success_rate: float) -> List[str]:
        """Generate insights from simulation results"""
        insights = []
        
        # Overall performance insight
        if success_rate > 0.7:
            insights.append("High success rate indicates effective agent coordination and learning")
        elif success_rate > 0.4:
            insights.append("Moderate success rate suggests room for improvement in agent strategies")
        else:
            insights.append("Low success rate indicates challenges in agent adaptation or environment complexity")
        
        # Learning agent insights
        for agent_id, report in agent_reports.items():
            if report["type"] == "learning":
                learning_stats = report["status"].get("learning_stats", {})
                avg_reward = learning_stats.get("average_reward", 0)
                if avg_reward > 0:
                    insights.append(f"Learning agent {agent_id} achieved positive average reward, showing successful adaptation")
                else:
                    insights.append(f"Learning agent {agent_id} struggled with negative rewards, may need parameter tuning")
        
        # Goal completion insights
        goal_agents = [aid for aid, report in agent_reports.items() if report["type"] == "goal_based"]
        if goal_agents:
            for agent_id in goal_agents:
                status = agent_reports[agent_id]["status"]
                completed = status.get("completed_goals", 0)
                active = status.get("active_goals", 0)
                if completed > 0:
                    insights.append(f"Goal-based agent {agent_id} completed {completed} goals, demonstrating effective planning")
        
        # Communication insights
        total_messages = sum(1 for report in agent_reports.values() 
                           if report["type"] in ["coordinator", "collaborative"])
        if total_messages > 50:
            insights.append("High communication activity indicates active coordination between agents")
        
        return insights

class AgentEnvironmentWrapper:
    """Wrapper to provide agent-specific environment interface"""
    
    def __init__(self, environment: GridWorld, agent_id: str):
        self.environment = environment
        self.agent_id = agent_id
    
    def get_state(self) -> Dict[str, Any]:
        """Get state from agent's perspective"""
        return self.environment.get_agent_local_state(self.agent_id)
    
    def execute_action(self, action) -> tuple:
        """Execute action with agent ID"""
        action.parameters["agent_id"] = self.agent_id
        return self.environment.execute_action(action)
    
    def get_available_actions(self) -> List[str]:
        """Get available actions"""
        return self.environment.get_available_actions()

def main():
    """Main function to run the Agentic AI demonstration"""
    print("=" * 60)
    print("AGENTIC AI DEMONSTRATION")
    print("=" * 60)
    print()
    print("This simulation demonstrates different types of Agentic AI:")
    print("1. Reactive Agents - Simple stimulus-response behavior")
    print("2. Goal-Based Agents - Plan actions to achieve objectives")
    print("3. Learning Agents - Improve through experience (Q-Learning)")
    print("4. Multi-Agent Systems - Coordination and communication")
    print()
    
    # Create and run simulation
    simulation = AgenticAISimulation(grid_size=10)
    
    # Run for 80 steps
    results = simulation.run_simulation(steps=80)
    
    # Print detailed results
    print("\n" + "=" * 60)
    print("SIMULATION RESULTS")
    print("=" * 60)
    
    summary = results["simulation_summary"]
    print(f"\nSimulation Summary:")
    print(f"  Total Steps: {summary['total_steps']}")
    print(f"  Simulation Time: {summary['simulation_time_seconds']:.2f} seconds")
    print(f"  Total Actions: {summary['total_actions']}")
    print(f"  Success Rate: {summary['success_rate']:.2%}")
    print(f"  Goals Completed: {summary['goals_completed']}")
    print(f"  Actions/Second: {summary['actions_per_second']:.1f}")
    
    print(f"\nAgent Performance:")
    for agent_id, report in results["agent_reports"].items():
        agent_type = report["type"]
        status = report["status"]
        print(f"  {agent_id} ({agent_type}):")
        
        if agent_type == "learning":
            learning_stats = status.get("learning_stats", {})
            print(f"    Average Reward: {learning_stats.get('average_reward', 0):.3f}")
            print(f"    Exploration Rate: {learning_stats.get('exploration_rate', 0):.3f}")
        
        elif agent_type == "goal_based":
            print(f"    Completed Goals: {status.get('completed_goals', 0)}")
            print(f"    Active Goals: {status.get('active_goals', 0)}")
        
        elif agent_type == "coordinator":
            print(f"    Managed Agents: {status.get('managed_agents', 0)}")
        
        elif agent_type == "collaborative":
            print(f"    Has Task: {status.get('current_task') is not None}")
            print(f"    Shared Knowledge: {status.get('shared_knowledge_items', 0)} items")
    
    print(f"\nCommunication Statistics:")
    comm_stats = results["communication_stats"]
    print(f"  Total Messages: {comm_stats['total_messages']}")
    print(f"  Message Types: {comm_stats['message_types']}")
    
    print(f"\nKey Insights:")
    for insight in results["key_insights"]:
        print(f"  • {insight}")
    
    print("\n" + "=" * 60)
    print("DEMONSTRATION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
