"""
Test script to verify all agent implementations work correctly.
Run this to ensure the Agentic AI system is functioning properly.
"""

import time
from agent_base import Goal, Action
from reactive_agent import ReactiveAgent
from goal_based_agent import GoalBasedAgent
from learning_agent import QLearningAgent
from multi_agent_system import CoordinatorAgent, CollaborativeAgent, CommunicationProtocol
from environment import GridWorld

def test_reactive_agent():
    """Test reactive agent functionality"""
    print("Testing Reactive Agent...")
    
    # Create environment and agent
    env = GridWorld(5, 5)
    agent = ReactiveAgent("test_reactive", "TestReactive")
    env.add_agent("test_reactive")
    
    # Create wrapper for agent-specific interface
    class TestEnvironmentWrapper:
        def __init__(self, env, agent_id):
            self.env = env
            self.agent_id = agent_id
        
        def get_state(self):
            return self.env.get_agent_local_state(self.agent_id)
        
        def execute_action(self, action):
            action.parameters["agent_id"] = self.agent_id
            return self.env.execute_action(action)
        
        def get_available_actions(self):
            return self.env.get_available_actions()
    
    wrapper = TestEnvironmentWrapper(env, "test_reactive")
    
    # Test agent cycle
    action = agent.run_cycle(wrapper)
    
    if action:
        print(f"  ✓ Reactive agent produced action: {action.action_type}")
        print(f"  ✓ Agent status: {agent.get_status()}")
        return True
    else:
        print("  ✗ Reactive agent failed to produce action")
        return False

def test_goal_based_agent():
    """Test goal-based agent functionality"""
    print("Testing Goal-Based Agent...")
    
    # Create environment and agent
    env = GridWorld(5, 5)
    agent = GoalBasedAgent("test_goal", "TestGoal")
    env.add_agent("test_goal")
    
    # Add a test goal
    test_goal = Goal(
        description="Test goal for collection",
        priority=1,
        success_criteria={"items_collected": 1}
    )
    agent.add_goal(test_goal)
    
    class TestEnvironmentWrapper:
        def __init__(self, env, agent_id):
            self.env = env
            self.agent_id = agent_id
        
        def get_state(self):
            return self.env.get_agent_local_state(self.agent_id)
        
        def execute_action(self, action):
            action.parameters["agent_id"] = self.agent_id
            return self.env.execute_action(action)
        
        def get_available_actions(self):
            return self.env.get_available_actions()
    
    wrapper = TestEnvironmentWrapper(env, "test_goal")
    
    # Test agent cycle
    action = agent.run_cycle(wrapper)
    
    if action:
        print(f"  ✓ Goal-based agent produced action: {action.action_type}")
        print(f"  ✓ Agent has {len(agent.get_active_goals())} active goals")
        print(f"  ✓ Agent status: {agent.get_status()}")
        return True
    else:
        print("  ✗ Goal-based agent failed to produce action")
        return False

def test_learning_agent():
    """Test learning agent functionality"""
    print("Testing Learning Agent...")
    
    # Create environment and agent
    env = GridWorld(5, 5)
    agent = QLearningAgent("test_learning", "TestLearning", epsilon=0.5)
    env.add_agent("test_learning")
    
    class TestEnvironmentWrapper:
        def __init__(self, env, agent_id):
            self.env = env
            self.agent_id = agent_id
        
        def get_state(self):
            return self.env.get_agent_local_state(self.agent_id)
        
        def execute_action(self, action):
            action.parameters["agent_id"] = self.agent_id
            return self.env.execute_action(action)
        
        def get_available_actions(self):
            return self.env.get_available_actions()
    
    wrapper = TestEnvironmentWrapper(env, "test_learning")
    
    # Test multiple cycles to see learning
    actions_taken = 0
    for i in range(5):
        action = agent.run_cycle(wrapper)
        if action:
            actions_taken += 1
    
    if actions_taken > 0:
        print(f"  ✓ Learning agent took {actions_taken} actions")
        print(f"  ✓ Q-table summary: {agent.get_q_table_summary()}")
        print(f"  ✓ Learning stats: {agent.learning_stats}")
        return True
    else:
        print("  ✗ Learning agent failed to take actions")
        return False

def test_multi_agent_system():
    """Test multi-agent system functionality"""
    print("Testing Multi-Agent System...")
    
    # Create communication protocol
    comm = CommunicationProtocol()
    
    # Create coordinator and collaborative agents
    coordinator = CoordinatorAgent("coord", "TestCoordinator", comm)
    collab1 = CollaborativeAgent("collab1", "TestCollab1", comm)
    collab2 = CollaborativeAgent("collab2", "TestCollab2", comm)
    
    # Set up relationships
    coordinator.add_managed_agent("collab1")
    coordinator.add_managed_agent("collab2")
    collab1.set_coordinator("coord")
    collab2.set_coordinator("coord")
    collab1.add_peer_agent("collab2")
    collab2.add_peer_agent("collab1")
    
    # Create environment
    env = GridWorld(5, 5)
    env.add_agent("coord")
    env.add_agent("collab1")
    env.add_agent("collab2")
    
    class TestEnvironmentWrapper:
        def __init__(self, env, agent_id):
            self.env = env
            self.agent_id = agent_id
        
        def get_state(self):
            return self.env.get_agent_local_state(self.agent_id)
        
        def execute_action(self, action):
            action.parameters["agent_id"] = self.agent_id
            return self.env.execute_action(action)
        
        def get_available_actions(self):
            return self.env.get_available_actions()
    
    # Test coordinator
    coord_wrapper = TestEnvironmentWrapper(env, "coord")
    coord_action = coordinator.run_cycle(coord_wrapper)
    
    # Test collaborative agents
    collab1_wrapper = TestEnvironmentWrapper(env, "collab1")
    collab1_action = collab1.run_cycle(collab1_wrapper)
    
    if coord_action or collab1_action:
        print(f"  ✓ Multi-agent system functioning")
        print(f"  ✓ Messages in system: {len(comm.message_history)}")
        print(f"  ✓ Coordinator status: {coordinator.get_status()}")
        print(f"  ✓ Collaborative agent status: {collab1.get_status()}")
        return True
    else:
        print("  ✗ Multi-agent system failed")
        return False

def test_environment():
    """Test environment functionality"""
    print("Testing Environment...")
    
    env = GridWorld(8, 8)
    
    # Add an agent
    success = env.add_agent("test_agent", 2, 2)
    
    if not success:
        print("  ✗ Failed to add agent to environment")
        return False
    
    # Test action execution
    test_action = Action(
        action_type="move_north",
        parameters={"agent_id": "test_agent"},
        timestamp=time.time()
    )
    
    success, result = env.execute_action(test_action)
    
    if success:
        print(f"  ✓ Environment executed action successfully")
        print(f"  ✓ Environment stats: {env.get_environment_stats()}")
        print(f"  ✓ Agent local state: {env.get_agent_local_state('test_agent')}")
        return True
    else:
        print(f"  ✗ Environment failed to execute action: {result}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("=" * 50)
    print("AGENTIC AI SYSTEM TESTS")
    print("=" * 50)
    print()
    
    tests = [
        ("Environment", test_environment),
        ("Reactive Agent", test_reactive_agent),
        ("Goal-Based Agent", test_goal_based_agent),
        ("Learning Agent", test_learning_agent),
        ("Multi-Agent System", test_multi_agent_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
            print()
    
    # Summary
    print("=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print()
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The Agentic AI system is working correctly.")
        print("\nYou can now run the full simulation with:")
        print("python simulation.py")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
