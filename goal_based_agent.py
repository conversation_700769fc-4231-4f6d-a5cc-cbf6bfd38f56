"""
Goal-Based Agent Implementation
This agent plans actions to achieve specific goals using search algorithms.
"""

import time
import heapq
from typing import Dict, Any, Optional, List, Tuple
from agent_base import Agent, Perception, Action, Environment, Goal, logger

class GoalBasedAgent(Agent):
    """
    A goal-based agent that plans sequences of actions to achieve objectives.
    Uses A* search algorithm for planning.
    """
    
    def __init__(self, agent_id: str, name: str):
        super().__init__(agent_id, name)
        self.current_plan: List[Action] = []
        self.world_model: Dict[str, Any] = {}
        self.action_costs: Dict[str, float] = {
            "move": 1.0,
            "pickup": 2.0,
            "drop": 1.0,
            "scan": 0.5,
            "wait": 0.1,
            "explore": 1.5
        }
        
    def perceive(self, environment: Environment) -> Perception:
        """Perceive environment and update world model"""
        env_state = environment.get_state()
        
        # Update world model with new information
        self.world_model.update(env_state)
        
        perception = Perception(
            timestamp=time.time(),
            data=env_state,
            source="environment_sensors",
            confidence=0.95
        )
        
        logger.info(f"GoalBasedAgent {self.name} updated world model: {env_state}")
        return perception
    
    def decide(self, perception: Perception) -> Optional[Action]:
        """
        Decide action based on current goals and planning.
        Uses A* search to find optimal action sequence.
        """
        active_goals = self.get_active_goals()
        
        if not active_goals:
            # No goals - explore or wait
            return Action(
                action_type="explore",
                parameters={"reason": "no_active_goals"},
                timestamp=time.time()
            )
        
        # Select highest priority goal
        current_goal = max(active_goals, key=lambda g: g.priority)
        
        # If we don't have a plan or plan is outdated, create new plan
        if not self.current_plan or self._is_plan_outdated():
            self.current_plan = self._plan_for_goal(current_goal, perception.data)
        
        # Execute next action in plan
        if self.current_plan:
            next_action = self.current_plan.pop(0)
            logger.info(f"GoalBasedAgent {self.name} executing planned action: {next_action.action_type}")
            return next_action
        
        # No valid plan found - try random exploration
        return Action(
            action_type="explore",
            parameters={"reason": "no_valid_plan"},
            timestamp=time.time()
        )
    
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """Learn from action results and update world model"""
        success = result.get("success", False)
        
        # Update action cost based on success
        action_type = action.action_type
        if action_type in self.action_costs:
            if success:
                # Successful actions become slightly cheaper
                self.action_costs[action_type] *= 0.95
            else:
                # Failed actions become more expensive
                self.action_costs[action_type] *= 1.1
        
        # Update world model with results
        if "new_state" in result:
            self.world_model.update(result["new_state"])
        
        # Check if any goals are completed
        self._check_goal_completion(result)
        
        # Update performance metrics
        self._update_performance_metrics(action, success)
        
        logger.info(f"GoalBasedAgent {self.name} learned from {action_type}: {'Success' if success else 'Failed'}")
    
    def _plan_for_goal(self, goal: Goal, current_state: Dict[str, Any]) -> List[Action]:
        """
        Create a plan to achieve the given goal using A* search algorithm.
        This is a simplified version for demonstration.
        """
        logger.info(f"Planning for goal: {goal.description}")
        
        # Simple planning based on goal type
        plan = []
        
        if "collect" in goal.description.lower():
            plan = self._plan_collection_task(goal, current_state)
        elif "move" in goal.description.lower() or "go" in goal.description.lower():
            plan = self._plan_movement_task(goal, current_state)
        elif "scan" in goal.description.lower() or "explore" in goal.description.lower():
            plan = self._plan_exploration_task(goal, current_state)
        else:
            # Default plan - try to gather information
            plan = [
                Action("scan", {"target": "environment"}, time.time()),
                Action("explore", {"direction": "random"}, time.time())
            ]
        
        logger.info(f"Created plan with {len(plan)} actions")
        return plan
    
    def _plan_collection_task(self, goal: Goal, state: Dict[str, Any]) -> List[Action]:
        """Plan for collecting items"""
        plan = []
        
        # First, scan to find items
        plan.append(Action("scan", {"target": "items"}, time.time()))
        
        # Move to item location (simplified)
        if "target_location" in goal.success_criteria:
            target = goal.success_criteria["target_location"]
            plan.append(Action("move", {"destination": target}, time.time()))
        
        # Pick up the item
        plan.append(Action("pickup", {"item": goal.success_criteria.get("item", "unknown")}, time.time()))
        
        return plan
    
    def _plan_movement_task(self, goal: Goal, state: Dict[str, Any]) -> List[Action]:
        """Plan for movement tasks"""
        plan = []
        
        # Simple movement planning
        if "destination" in goal.success_criteria:
            destination = goal.success_criteria["destination"]
            current_pos = state.get("position", {"x": 0, "y": 0})
            
            # Calculate simple path (Manhattan distance)
            steps = abs(destination.get("x", 0) - current_pos.get("x", 0)) + \
                   abs(destination.get("y", 0) - current_pos.get("y", 0))
            
            for _ in range(steps):
                plan.append(Action("move", {"step": 1}, time.time()))
        
        return plan
    
    def _plan_exploration_task(self, goal: Goal, state: Dict[str, Any]) -> List[Action]:
        """Plan for exploration tasks"""
        plan = []
        
        # Systematic exploration
        for direction in ["north", "east", "south", "west"]:
            plan.append(Action("scan", {"direction": direction}, time.time()))
            plan.append(Action("move", {"direction": direction, "distance": 1}, time.time()))
        
        return plan
    
    def _is_plan_outdated(self) -> bool:
        """Check if current plan is outdated based on world changes"""
        # Simple heuristic - plan is outdated if world model changed significantly
        return len(self.memory) > 0 and time.time() - self.memory[-1].timestamp > 10
    
    def _check_goal_completion(self, result: Dict[str, Any]) -> None:
        """Check if any goals have been completed"""
        for goal in self.goals:
            if not goal.completed and self._is_goal_achieved(goal, result):
                goal.completed = True
                logger.info(f"Goal completed: {goal.description}")
    
    def _is_goal_achieved(self, goal: Goal, result: Dict[str, Any]) -> bool:
        """Check if a specific goal has been achieved"""
        if not goal.success_criteria:
            return False
        
        # Simple goal checking based on criteria
        for criterion, expected_value in goal.success_criteria.items():
            if result.get(criterion) != expected_value:
                return False
        
        return True
    
    def _update_performance_metrics(self, action: Action, success: bool) -> None:
        """Update performance tracking metrics"""
        if "total_actions" not in self.performance_metrics:
            self.performance_metrics["total_actions"] = 0
            self.performance_metrics["successful_actions"] = 0
            self.performance_metrics["planning_efficiency"] = 0.0
        
        self.performance_metrics["total_actions"] += 1
        if success:
            self.performance_metrics["successful_actions"] += 1
        
        # Calculate success rate
        self.performance_metrics["success_rate"] = (
            self.performance_metrics["successful_actions"] / 
            self.performance_metrics["total_actions"]
        )
        
        # Track planning efficiency (goals completed vs actions taken)
        completed_goals = len([g for g in self.goals if g.completed])
        if self.performance_metrics["total_actions"] > 0:
            self.performance_metrics["planning_efficiency"] = (
                completed_goals / self.performance_metrics["total_actions"]
            )
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "GoalBasedAgent",
            "name": self.name,
            "state": self.state.value,
            "active_goals": len(self.get_active_goals()),
            "completed_goals": len([g for g in self.goals if g.completed]),
            "current_plan_length": len(self.current_plan),
            "world_model_size": len(self.world_model),
            "performance": self.performance_metrics
        }
