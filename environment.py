"""
Environment Implementation
Simulates a world where agents can operate and interact.
"""

import time
import random
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from agent_base import Environment, Action, logger

class GridWorld(Environment):
    """
    A simple grid-based environment for testing agents.
    Agents can move around, collect items, and interact with objects.
    """
    
    def __init__(self, width: int = 10, height: int = 10):
        self.width = width
        self.height = height
        self.grid = np.zeros((height, width), dtype=int)
        
        # Environment state
        self.agents: Dict[str, Dict[str, Any]] = {}
        self.items: Dict[Tuple[int, int], str] = {}
        self.obstacles: List[Tuple[int, int]] = []
        self.time_step = 0
        
        # Initialize environment
        self._initialize_environment()
        
    def _initialize_environment(self) -> None:
        """Initialize the environment with items and obstacles"""
        # Add some random obstacles
        num_obstacles = random.randint(5, 15)
        for _ in range(num_obstacles):
            x, y = random.randint(0, self.width-1), random.randint(0, self.height-1)
            if (x, y) not in self.obstacles:
                self.obstacles.append((x, y))
                self.grid[y, x] = -1  # Mark as obstacle
        
        # Add some random items
        item_types = ["energy_cell", "data_chip", "repair_kit", "sensor"]
        num_items = random.randint(8, 20)
        for _ in range(num_items):
            x, y = random.randint(0, self.width-1), random.randint(0, self.height-1)
            if (x, y) not in self.obstacles and (x, y) not in self.items:
                item_type = random.choice(item_types)
                self.items[(x, y)] = item_type
                self.grid[y, x] = 1  # Mark as item
        
        logger.info(f"GridWorld initialized: {self.width}x{self.height}, {len(self.obstacles)} obstacles, {len(self.items)} items")
    
    def add_agent(self, agent_id: str, x: int = None, y: int = None) -> bool:
        """Add an agent to the environment"""
        if x is None or y is None:
            # Find random empty position
            while True:
                x, y = random.randint(0, self.width-1), random.randint(0, self.height-1)
                if (x, y) not in self.obstacles and (x, y) not in self.items:
                    break
        
        if not self._is_valid_position(x, y):
            return False
        
        self.agents[agent_id] = {
            "position": {"x": x, "y": y},
            "battery_level": 100,
            "inventory": [],
            "last_action": None,
            "action_count": 0
        }
        
        logger.info(f"Agent {agent_id} added to GridWorld at position ({x}, {y})")
        return True
    
    def get_state(self) -> Dict[str, Any]:
        """Get current environment state"""
        return {
            "time_step": self.time_step,
            "grid_size": {"width": self.width, "height": self.height},
            "agents": self.agents.copy(),
            "items": dict(self.items),
            "obstacles": self.obstacles.copy(),
            "total_items": len(self.items)
        }
    
    def get_agent_local_state(self, agent_id: str) -> Dict[str, Any]:
        """Get local state visible to a specific agent"""
        if agent_id not in self.agents:
            return {}
        
        agent = self.agents[agent_id]
        pos = agent["position"]
        
        # Agent can see 3x3 area around itself
        visible_area = self._get_visible_area(pos["x"], pos["y"], radius=1)
        
        # Find nearby items and obstacles
        nearby_items = []
        nearby_obstacles = []
        
        for (x, y) in visible_area:
            if (x, y) in self.items:
                nearby_items.append({"position": {"x": x, "y": y}, "type": self.items[(x, y)]})
            if (x, y) in self.obstacles:
                nearby_obstacles.append({"x": x, "y": y})
        
        # Check for other agents nearby
        nearby_agents = []
        for other_id, other_agent in self.agents.items():
            if other_id != agent_id:
                other_pos = other_agent["position"]
                distance = abs(other_pos["x"] - pos["x"]) + abs(other_pos["y"] - pos["y"])
                if distance <= 2:  # Within 2 steps
                    nearby_agents.append({
                        "id": other_id,
                        "position": other_pos,
                        "distance": distance
                    })
        
        return {
            "position": pos,
            "battery_level": agent["battery_level"],
            "inventory": agent["inventory"],
            "nearby_items": nearby_items,
            "nearby_obstacles": nearby_obstacles,
            "nearby_agents": nearby_agents,
            "visible_area_size": len(visible_area),
            "motion": agent["action_count"] > 0,  # Has the agent moved recently?
            "temperature": random.randint(18, 28),  # Simulated temperature
            "obstacle_distance": self._get_nearest_obstacle_distance(pos["x"], pos["y"])
        }
    
    def execute_action(self, action: Action) -> Tuple[bool, Dict[str, Any]]:
        """Execute an action in the environment"""
        self.time_step += 1
        
        # Extract agent ID from action parameters
        agent_id = action.parameters.get("agent_id", "unknown")
        
        if agent_id not in self.agents:
            return False, {"error": "Agent not found", "success": False}
        
        agent = self.agents[agent_id]
        result = {"success": False, "new_state": {}, "agent_id": agent_id}
        
        # Execute specific action
        if action.action_type.startswith("move"):
            result = self._execute_move_action(agent_id, action)
        elif action.action_type == "pickup":
            result = self._execute_pickup_action(agent_id, action)
        elif action.action_type == "drop":
            result = self._execute_drop_action(agent_id, action)
        elif action.action_type == "scan":
            result = self._execute_scan_action(agent_id, action)
        elif action.action_type in ["wait", "explore"]:
            result = self._execute_passive_action(agent_id, action)
        else:
            result = {"success": False, "error": f"Unknown action: {action.action_type}"}
        
        # Update agent state
        agent["last_action"] = action.action_type
        agent["action_count"] += 1
        agent["battery_level"] = max(0, agent["battery_level"] - 1)  # Battery drain
        
        # Add current state to result
        result["new_state"] = self.get_agent_local_state(agent_id)
        result["battery_level"] = agent["battery_level"]
        
        return result["success"], result
    
    def _execute_move_action(self, agent_id: str, action: Action) -> Dict[str, Any]:
        """Execute movement action"""
        agent = self.agents[agent_id]
        current_pos = agent["position"]
        
        # Determine new position based on action
        new_x, new_y = current_pos["x"], current_pos["y"]
        
        if action.action_type == "move_north":
            new_y = max(0, new_y - 1)
        elif action.action_type == "move_south":
            new_y = min(self.height - 1, new_y + 1)
        elif action.action_type == "move_east":
            new_x = min(self.width - 1, new_x + 1)
        elif action.action_type == "move_west":
            new_x = max(0, new_x - 1)
        elif action.action_type == "move":
            # Generic move with direction parameter
            direction = action.parameters.get("direction", "north")
            if direction == "north":
                new_y = max(0, new_y - 1)
            elif direction == "south":
                new_y = min(self.height - 1, new_y + 1)
            elif direction == "east":
                new_x = min(self.width - 1, new_x + 1)
            elif direction == "west":
                new_x = max(0, new_x - 1)
        
        # Check if new position is valid
        if (new_x, new_y) in self.obstacles:
            return {"success": False, "error": "Obstacle blocking path", "obstacle_hit": True}
        
        # Update position
        agent["position"] = {"x": new_x, "y": new_y}
        
        # Check if moved to a new area
        new_area_discovered = (new_x != current_pos["x"] or new_y != current_pos["y"])
        
        return {
            "success": True,
            "new_position": {"x": new_x, "y": new_y},
            "new_area_discovered": new_area_discovered,
            "distance_moved": abs(new_x - current_pos["x"]) + abs(new_y - current_pos["y"])
        }
    
    def _execute_pickup_action(self, agent_id: str, action: Action) -> Dict[str, Any]:
        """Execute pickup action"""
        agent = self.agents[agent_id]
        pos = agent["position"]
        pos_tuple = (pos["x"], pos["y"])
        
        if pos_tuple not in self.items:
            return {"success": False, "error": "No item at current position"}
        
        # Pick up the item
        item_type = self.items[pos_tuple]
        agent["inventory"].append(item_type)
        del self.items[pos_tuple]
        self.grid[pos["y"], pos["x"]] = 0  # Clear grid cell
        
        return {
            "success": True,
            "item_picked": item_type,
            "inventory_size": len(agent["inventory"]),
            "goal_completed": len(agent["inventory"]) >= 3  # Simple goal
        }
    
    def _execute_drop_action(self, agent_id: str, action: Action) -> Dict[str, Any]:
        """Execute drop action"""
        agent = self.agents[agent_id]
        
        if not agent["inventory"]:
            return {"success": False, "error": "No items in inventory"}
        
        # Drop the last item
        item_type = agent["inventory"].pop()
        pos = agent["position"]
        self.items[(pos["x"], pos["y"])] = item_type
        self.grid[pos["y"], pos["x"]] = 1
        
        return {
            "success": True,
            "item_dropped": item_type,
            "inventory_size": len(agent["inventory"])
        }
    
    def _execute_scan_action(self, agent_id: str, action: Action) -> Dict[str, Any]:
        """Execute scan action"""
        agent = self.agents[agent_id]
        pos = agent["position"]
        
        # Scan larger area
        visible_area = self._get_visible_area(pos["x"], pos["y"], radius=2)
        
        scanned_items = []
        scanned_obstacles = []
        
        for (x, y) in visible_area:
            if (x, y) in self.items:
                scanned_items.append({"position": {"x": x, "y": y}, "type": self.items[(x, y)]})
            if (x, y) in self.obstacles:
                scanned_obstacles.append({"x": x, "y": y})
        
        return {
            "success": True,
            "scanned_items": scanned_items,
            "scanned_obstacles": scanned_obstacles,
            "scan_radius": 2,
            "new_knowledge": {
                "items_discovered": len(scanned_items),
                "obstacles_mapped": len(scanned_obstacles)
            }
        }
    
    def _execute_passive_action(self, agent_id: str, action: Action) -> Dict[str, Any]:
        """Execute passive actions like wait or explore"""
        agent = self.agents[agent_id]
        
        if action.action_type == "wait":
            # Restore some battery while waiting
            agent["battery_level"] = min(100, agent["battery_level"] + 2)
            return {"success": True, "battery_restored": 2}
        
        elif action.action_type == "explore":
            # Random exploration - move in random direction
            directions = ["north", "south", "east", "west"]
            direction = random.choice(directions)
            
            # Create a move action and execute it
            move_action = Action(
                action_type="move",
                parameters={"direction": direction, "agent_id": agent_id},
                timestamp=time.time()
            )
            
            return self._execute_move_action(agent_id, move_action)
        
        return {"success": True}
    
    def _get_visible_area(self, x: int, y: int, radius: int) -> List[Tuple[int, int]]:
        """Get list of positions visible from given position"""
        visible = []
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                new_x, new_y = x + dx, y + dy
                if self._is_valid_position(new_x, new_y):
                    visible.append((new_x, new_y))
        return visible
    
    def _is_valid_position(self, x: int, y: int) -> bool:
        """Check if position is within grid bounds"""
        return 0 <= x < self.width and 0 <= y < self.height
    
    def _get_nearest_obstacle_distance(self, x: int, y: int) -> int:
        """Get distance to nearest obstacle"""
        if not self.obstacles:
            return 100
        
        min_distance = float('inf')
        for obs_x, obs_y in self.obstacles:
            distance = abs(x - obs_x) + abs(y - obs_y)
            min_distance = min(min_distance, distance)
        
        return int(min_distance)
    
    def get_available_actions(self) -> List[str]:
        """Get list of available actions"""
        return [
            "move_north", "move_south", "move_east", "move_west",
            "pickup", "drop", "scan", "wait", "explore"
        ]
    
    def get_environment_stats(self) -> Dict[str, Any]:
        """Get environment statistics"""
        return {
            "time_step": self.time_step,
            "active_agents": len(self.agents),
            "remaining_items": len(self.items),
            "total_obstacles": len(self.obstacles),
            "grid_size": f"{self.width}x{self.height}",
            "agent_positions": {aid: agent["position"] for aid, agent in self.agents.items()}
        }
