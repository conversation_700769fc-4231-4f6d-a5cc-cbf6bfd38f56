# Agentic AI System - API Specification

## Overview
This document provides detailed API specifications for the Agentic AI system, including class interfaces, method signatures, and usage examples.

## Core Interfaces

### Agent Base Interface

#### `Agent` (Abstract Base Class)

```python
class Agent(ABC):
    """Abstract base class for all autonomous agents."""
    
    def __init__(self, agent_id: str, name: str) -> None:
        """
        Initialize agent with unique identifier and name.
        
        Args:
            agent_id: Unique identifier for the agent
            name: Human-readable name for the agent
        """
    
    @abstractmethod
    def perceive(self, environment: Environment) -> Perception:
        """
        Perceive the current environment state.
        
        Args:
            environment: Environment instance to perceive from
            
        Returns:
            Perception: Structured perception data
            
        Raises:
            PerceptionError: If perception fails
        """
    
    @abstractmethod
    def decide(self, perception: Perception) -> Optional[Action]:
        """
        Decide what action to take based on perception.
        
        Args:
            perception: Current perception of environment
            
        Returns:
            Optional[Action]: Action to execute, None if no action
            
        Raises:
            DecisionError: If decision process fails
        """
    
    @abstractmethod
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """
        Learn from the result of an executed action.
        
        Args:
            action: The action that was executed
            result: Result of action execution
            
        Raises:
            LearningError: If learning process fails
        """
    
    def run_cycle(self, environment: Environment) -> Optional[Action]:
        """
        Execute one complete agent cycle: perceive -> decide -> act -> learn.
        
        Args:
            environment: Environment to interact with
            
        Returns:
            Optional[Action]: Action that was executed
            
        Raises:
            AgentCycleError: If cycle execution fails
        """
    
    def add_goal(self, goal: Goal) -> None:
        """
        Add a new goal to the agent's goal set.
        
        Args:
            goal: Goal to add
            
        Raises:
            GoalError: If goal is invalid
        """
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current agent status and metrics.
        
        Returns:
            Dict containing agent status information
        """
```

### Environment Interface

#### `Environment` (Abstract Base Class)

```python
class Environment(ABC):
    """Abstract base class for agent environments."""
    
    @abstractmethod
    def get_state(self) -> Dict[str, Any]:
        """
        Get current environment state.
        
        Returns:
            Dict containing complete environment state
            
        Raises:
            EnvironmentError: If state retrieval fails
        """
    
    @abstractmethod
    def execute_action(self, action: Action) -> Tuple[bool, Dict[str, Any]]:
        """
        Execute an action and return results.
        
        Args:
            action: Action to execute
            
        Returns:
            Tuple of (success: bool, result: Dict)
            
        Raises:
            ActionExecutionError: If action execution fails
        """
    
    @abstractmethod
    def get_available_actions(self) -> List[str]:
        """
        Get list of available action types.
        
        Returns:
            List of action type strings
        """
```

#### `GridWorld` (Concrete Implementation)

```python
class GridWorld(Environment):
    """Grid-based environment implementation."""
    
    def __init__(self, width: int = 10, height: int = 10) -> None:
        """
        Initialize grid world with specified dimensions.
        
        Args:
            width: Grid width (default: 10)
            height: Grid height (default: 10)
            
        Raises:
            ValueError: If dimensions are invalid
        """
    
    def add_agent(self, agent_id: str, x: int = None, y: int = None) -> bool:
        """
        Add an agent to the environment.
        
        Args:
            agent_id: Unique agent identifier
            x: Initial x position (random if None)
            y: Initial y position (random if None)
            
        Returns:
            bool: True if agent added successfully
            
        Raises:
            AgentPlacementError: If agent cannot be placed
        """
    
    def get_agent_local_state(self, agent_id: str) -> Dict[str, Any]:
        """
        Get local state visible to specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Dict containing agent's local view
            
        Raises:
            AgentNotFoundError: If agent doesn't exist
        """
    
    def get_environment_stats(self) -> Dict[str, Any]:
        """
        Get environment statistics and metrics.
        
        Returns:
            Dict containing environment statistics
        """
```

### Data Structures

#### `Perception`

```python
@dataclass
class Perception:
    """Represents agent perception of environment."""
    
    timestamp: float
    """Unix timestamp when perception was created."""
    
    data: Dict[str, Any]
    """Perception data dictionary."""
    
    source: str
    """Source of perception (e.g., 'environment_sensors')."""
    
    confidence: float = 1.0
    """Confidence level of perception (0.0 to 1.0)."""
    
    def is_valid(self) -> bool:
        """Check if perception is valid."""
        return (
            self.timestamp > 0 and
            isinstance(self.data, dict) and
            0.0 <= self.confidence <= 1.0
        )
```

#### `Action`

```python
@dataclass
class Action:
    """Represents an action an agent can take."""
    
    action_type: str
    """Type of action (e.g., 'move', 'pickup')."""
    
    parameters: Dict[str, Any]
    """Action parameters dictionary."""
    
    timestamp: float
    """Unix timestamp when action was created."""
    
    expected_outcome: Optional[str] = None
    """Expected outcome description."""
    
    def validate(self) -> bool:
        """Validate action parameters."""
        return (
            bool(self.action_type) and
            isinstance(self.parameters, dict) and
            self.timestamp > 0
        )
```

#### `Goal`

```python
@dataclass
class Goal:
    """Represents an agent goal."""
    
    description: str
    """Human-readable goal description."""
    
    priority: int
    """Goal priority (higher = more important)."""
    
    deadline: Optional[float] = None
    """Optional deadline (Unix timestamp)."""
    
    success_criteria: Dict[str, Any] = None
    """Criteria for goal completion."""
    
    completed: bool = False
    """Whether goal has been completed."""
    
    def is_expired(self) -> bool:
        """Check if goal has expired."""
        if self.deadline is None:
            return False
        return time.time() > self.deadline
    
    def check_completion(self, state: Dict[str, Any]) -> bool:
        """Check if goal is completed based on state."""
        if not self.success_criteria:
            return False
        
        for criterion, expected in self.success_criteria.items():
            if state.get(criterion) != expected:
                return False
        
        return True
```

## Agent-Specific APIs

### ReactiveAgent

```python
class ReactiveAgent(Agent):
    """Simple reactive agent implementation."""
    
    def __init__(self, agent_id: str, name: str, 
                 reaction_rules: Dict[str, str] = None) -> None:
        """
        Initialize reactive agent with reaction rules.
        
        Args:
            agent_id: Unique agent identifier
            name: Agent name
            reaction_rules: Condition -> Action mapping
        """
    
    def add_reaction_rule(self, condition: str, action: str) -> None:
        """
        Add a new reaction rule.
        
        Args:
            condition: Condition to check
            action: Action to take when condition is met
        """
    
    def remove_reaction_rule(self, condition: str) -> bool:
        """
        Remove a reaction rule.
        
        Args:
            condition: Condition to remove
            
        Returns:
            bool: True if rule was removed
        """
```

### GoalBasedAgent

```python
class GoalBasedAgent(Agent):
    """Goal-oriented planning agent."""
    
    def __init__(self, agent_id: str, name: str) -> None:
        """Initialize goal-based agent."""
    
    def set_action_cost(self, action_type: str, cost: float) -> None:
        """
        Set cost for specific action type.
        
        Args:
            action_type: Type of action
            cost: Cost value (positive float)
        """
    
    def get_current_plan(self) -> List[Action]:
        """
        Get current action plan.
        
        Returns:
            List of planned actions
        """
    
    def clear_plan(self) -> None:
        """Clear current action plan."""
    
    def get_world_model(self) -> Dict[str, Any]:
        """
        Get agent's internal world model.
        
        Returns:
            Dict containing world model state
        """
```

### QLearningAgent

```python
class QLearningAgent(Agent):
    """Q-Learning reinforcement learning agent."""
    
    def __init__(self, agent_id: str, name: str, 
                 learning_rate: float = 0.1,
                 discount_factor: float = 0.9,
                 epsilon: float = 0.1) -> None:
        """
        Initialize Q-Learning agent.
        
        Args:
            agent_id: Unique agent identifier
            name: Agent name
            learning_rate: Learning rate (alpha)
            discount_factor: Discount factor (gamma)
            epsilon: Exploration rate
        """
    
    def get_q_value(self, state: str, action: str) -> float:
        """
        Get Q-value for state-action pair.
        
        Args:
            state: State key
            action: Action type
            
        Returns:
            Q-value for the state-action pair
        """
    
    def set_q_value(self, state: str, action: str, value: float) -> None:
        """
        Set Q-value for state-action pair.
        
        Args:
            state: State key
            action: Action type
            value: Q-value to set
        """
    
    def get_q_table_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics of Q-table.
        
        Returns:
            Dict containing Q-table statistics
        """
    
    def save_q_table(self, filepath: str) -> None:
        """
        Save Q-table to file.
        
        Args:
            filepath: Path to save Q-table
        """
    
    def load_q_table(self, filepath: str) -> None:
        """
        Load Q-table from file.
        
        Args:
            filepath: Path to load Q-table from
        """
```

## Communication API

### Message System

```python
@dataclass
class Message:
    """Message for agent communication."""
    
    sender_id: str
    receiver_id: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: float
    priority: int = 1
    
    def is_expired(self, ttl: float = 60.0) -> bool:
        """Check if message has expired."""
        return time.time() - self.timestamp > ttl
```

```python
class MessageType(Enum):
    """Types of messages agents can send."""
    
    INFORMATION = "information"
    REQUEST = "request"
    RESPONSE = "response"
    COORDINATION = "coordination"
    ALERT = "alert"
```

### CommunicationProtocol

```python
class CommunicationProtocol:
    """Handles message passing between agents."""
    
    def __init__(self) -> None:
        """Initialize communication protocol."""
    
    def send_message(self, message: Message) -> bool:
        """
        Send a message.
        
        Args:
            message: Message to send
            
        Returns:
            bool: True if message sent successfully
        """
    
    def get_messages_for_agent(self, agent_id: str) -> List[Message]:
        """
        Get all messages for specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            List of messages for the agent
        """
    
    def broadcast_message(self, sender_id: str, message_type: MessageType,
                         content: Dict[str, Any], agent_ids: List[str]) -> None:
        """
        Broadcast message to multiple agents.
        
        Args:
            sender_id: Sender agent ID
            message_type: Type of message
            content: Message content
            agent_ids: List of recipient agent IDs
        """
    
    def get_message_history(self, limit: int = 100) -> List[Message]:
        """
        Get message history.
        
        Args:
            limit: Maximum number of messages to return
            
        Returns:
            List of recent messages
        """
    
    def clear_message_queue(self) -> None:
        """Clear all pending messages."""
```

## Simulation API

### AgenticAISimulation

```python
class AgenticAISimulation:
    """Main simulation controller."""
    
    def __init__(self, grid_size: int = 12) -> None:
        """
        Initialize simulation.
        
        Args:
            grid_size: Size of the grid environment
        """
    
    def add_agent(self, agent: Agent) -> bool:
        """
        Add agent to simulation.
        
        Args:
            agent: Agent instance to add
            
        Returns:
            bool: True if agent added successfully
        """
    
    def remove_agent(self, agent_id: str) -> bool:
        """
        Remove agent from simulation.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            bool: True if agent removed successfully
        """
    
    def run_simulation(self, steps: int = None) -> Dict[str, Any]:
        """
        Run simulation for specified steps.
        
        Args:
            steps: Number of simulation steps (None for default)
            
        Returns:
            Dict containing simulation results
        """
    
    def pause_simulation(self) -> None:
        """Pause the simulation."""
    
    def resume_simulation(self) -> None:
        """Resume the simulation."""
    
    def get_simulation_state(self) -> Dict[str, Any]:
        """
        Get current simulation state.
        
        Returns:
            Dict containing simulation state
        """
    
    def export_results(self, filepath: str, format: str = "json") -> None:
        """
        Export simulation results.
        
        Args:
            filepath: Path to export file
            format: Export format ('json', 'csv', 'pickle')
        """
```

## Error Handling

### Custom Exceptions

```python
class AgenticAIError(Exception):
    """Base exception for Agentic AI system."""
    pass

class AgentError(AgenticAIError):
    """Base exception for agent-related errors."""
    pass

class PerceptionError(AgentError):
    """Exception raised during perception phase."""
    pass

class DecisionError(AgentError):
    """Exception raised during decision phase."""
    pass

class LearningError(AgentError):
    """Exception raised during learning phase."""
    pass

class EnvironmentError(AgenticAIError):
    """Exception raised by environment operations."""
    pass

class ActionExecutionError(EnvironmentError):
    """Exception raised during action execution."""
    pass

class CommunicationError(AgenticAIError):
    """Exception raised during agent communication."""
    pass

class SimulationError(AgenticAIError):
    """Exception raised during simulation execution."""
    pass
```

## Usage Examples

### Basic Agent Creation

```python
# Create reactive agent
reactive_agent = ReactiveAgent("agent_1", "ReactiveBot")
reactive_agent.add_reaction_rule("temperature_high", "turn_on_cooling")

# Create goal-based agent
goal_agent = GoalBasedAgent("agent_2", "PlannerBot")
goal = Goal("Collect items", priority=1, 
           success_criteria={"items_collected": 3})
goal_agent.add_goal(goal)

# Create learning agent
learning_agent = QLearningAgent("agent_3", "LearnerBot",
                               learning_rate=0.1, epsilon=0.2)
```

### Environment Setup

```python
# Create environment
env = GridWorld(width=10, height=10)

# Add agents to environment
env.add_agent("agent_1", x=0, y=0)
env.add_agent("agent_2", x=5, y=5)
env.add_agent("agent_3")  # Random position
```

### Running Simulation

```python
# Create simulation
simulation = AgenticAISimulation(grid_size=10)

# Add agents
simulation.add_agent(reactive_agent)
simulation.add_agent(goal_agent)
simulation.add_agent(learning_agent)

# Run simulation
results = simulation.run_simulation(steps=100)

# Export results
simulation.export_results("results.json", format="json")
```

This API specification provides comprehensive documentation for all public interfaces in the Agentic AI system, enabling developers to effectively use and extend the system.
