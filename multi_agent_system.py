"""
Multi-Agent System Implementation
Demonstrates coordination and communication between multiple agents.
"""

import time
import random
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from agent_base import Agent, Perception, Action, Environment, Goal, logger

class MessageType(Enum):
    """Types of messages agents can send"""
    INFORMATION = "information"
    REQUEST = "request"
    RESPONSE = "response"
    COORDINATION = "coordination"
    ALERT = "alert"

@dataclass
class Message:
    """Message structure for agent communication"""
    sender_id: str
    receiver_id: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: float
    priority: int = 1

class CommunicationProtocol:
    """Handles message passing between agents"""
    
    def __init__(self):
        self.message_queue: List[Message] = []
        self.message_history: List[Message] = []
        
    def send_message(self, message: Message) -> bool:
        """Send a message to the queue"""
        self.message_queue.append(message)
        self.message_history.append(message)
        logger.info(f"Message sent from {message.sender_id} to {message.receiver_id}: {message.message_type.value}")
        return True
    
    def get_messages_for_agent(self, agent_id: str) -> List[Message]:
        """Get all messages for a specific agent"""
        messages = [msg for msg in self.message_queue if msg.receiver_id == agent_id]
        # Remove retrieved messages from queue
        self.message_queue = [msg for msg in self.message_queue if msg.receiver_id != agent_id]
        return messages
    
    def broadcast_message(self, sender_id: str, message_type: MessageType, 
                         content: Dict[str, Any], agent_ids: List[str]) -> None:
        """Broadcast a message to multiple agents"""
        for agent_id in agent_ids:
            if agent_id != sender_id:  # Don't send to self
                message = Message(
                    sender_id=sender_id,
                    receiver_id=agent_id,
                    message_type=message_type,
                    content=content,
                    timestamp=time.time()
                )
                self.send_message(message)

class CoordinatorAgent(Agent):
    """
    A coordinator agent that manages and coordinates other agents.
    Demonstrates hierarchical multi-agent systems.
    """
    
    def __init__(self, agent_id: str, name: str, communication_protocol: CommunicationProtocol):
        super().__init__(agent_id, name)
        self.communication = communication_protocol
        self.managed_agents: List[str] = []
        self.task_assignments: Dict[str, Dict[str, Any]] = {}
        self.agent_status: Dict[str, Dict[str, Any]] = {}
        
    def add_managed_agent(self, agent_id: str) -> None:
        """Add an agent to be managed by this coordinator"""
        self.managed_agents.append(agent_id)
        logger.info(f"Coordinator {self.name} now managing agent {agent_id}")
    
    def perceive(self, environment: Environment) -> Perception:
        """Perceive environment and process incoming messages"""
        env_state = environment.get_state()
        
        # Process incoming messages
        messages = self.communication.get_messages_for_agent(self.agent_id)
        
        perception_data = {
            "environment": env_state,
            "messages": messages,
            "managed_agents": len(self.managed_agents),
            "active_tasks": len(self.task_assignments)
        }
        
        # Update agent status based on messages
        for message in messages:
            if message.message_type == MessageType.INFORMATION:
                self.agent_status[message.sender_id] = message.content
        
        return Perception(
            timestamp=time.time(),
            data=perception_data,
            source="coordinator_sensors",
            confidence=0.9
        )
    
    def decide(self, perception: Perception) -> Optional[Action]:
        """Coordinate agents and assign tasks"""
        messages = perception.data.get("messages", [])
        
        # Process coordination requests
        for message in messages:
            if message.message_type == MessageType.REQUEST:
                self._handle_agent_request(message)
        
        # Check if we need to assign new tasks
        if self._should_assign_tasks():
            return self._create_task_assignment_action()
        
        # Monitor agent performance
        if self._should_monitor_agents():
            return self._create_monitoring_action()
        
        return Action(
            action_type="coordinate",
            parameters={"status": "monitoring"},
            timestamp=time.time()
        )
    
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """Learn from coordination results"""
        success = result.get("success", False)
        
        # Update coordination effectiveness
        if "coordination_success_rate" not in self.performance_metrics:
            self.performance_metrics["coordination_success_rate"] = 0.0
            self.performance_metrics["total_coordinations"] = 0
        
        self.performance_metrics["total_coordinations"] += 1
        if success:
            current_rate = self.performance_metrics["coordination_success_rate"]
            total = self.performance_metrics["total_coordinations"]
            self.performance_metrics["coordination_success_rate"] = (
                (current_rate * (total - 1) + 1.0) / total
            )
        
        logger.info(f"Coordinator {self.name} coordination result: {'Success' if success else 'Failed'}")
    
    def _handle_agent_request(self, message: Message) -> None:
        """Handle requests from managed agents"""
        request_type = message.content.get("request_type")
        
        if request_type == "task_assignment":
            self._assign_task_to_agent(message.sender_id, message.content)
        elif request_type == "help":
            self._provide_help_to_agent(message.sender_id, message.content)
        elif request_type == "resource":
            self._allocate_resource_to_agent(message.sender_id, message.content)
    
    def _assign_task_to_agent(self, agent_id: str, request_content: Dict[str, Any]) -> None:
        """Assign a task to a specific agent"""
        task = {
            "task_id": f"task_{time.time()}",
            "description": request_content.get("task_description", "General task"),
            "priority": request_content.get("priority", 1),
            "deadline": time.time() + 300,  # 5 minutes
            "assigned_at": time.time()
        }
        
        self.task_assignments[agent_id] = task
        
        # Send task assignment message
        response = Message(
            sender_id=self.agent_id,
            receiver_id=agent_id,
            message_type=MessageType.RESPONSE,
            content={"task_assignment": task, "status": "assigned"},
            timestamp=time.time()
        )
        
        self.communication.send_message(response)
        logger.info(f"Coordinator assigned task {task['task_id']} to agent {agent_id}")

    def _provide_help_to_agent(self, agent_id: str, request_content: Dict[str, Any]) -> None:
        """Provide help to an agent that requested it"""
        # Simple help response
        help_response = Message(
            sender_id=self.agent_id,
            receiver_id=agent_id,
            message_type=MessageType.RESPONSE,
            content={"help_provided": True, "advice": "Continue with current task"},
            timestamp=time.time()
        )
        self.communication.send_message(help_response)

    def _allocate_resource_to_agent(self, agent_id: str, request_content: Dict[str, Any]) -> None:
        """Allocate resources to an agent"""
        # Simple resource allocation
        resource_response = Message(
            sender_id=self.agent_id,
            receiver_id=agent_id,
            message_type=MessageType.RESPONSE,
            content={"resource_allocated": True, "resource_type": "general"},
            timestamp=time.time()
        )
        self.communication.send_message(resource_response)

    def _should_assign_tasks(self) -> bool:
        """Determine if new tasks should be assigned"""
        # Simple heuristic: assign tasks if agents are idle
        return len(self.task_assignments) < len(self.managed_agents)
    
    def _should_monitor_agents(self) -> bool:
        """Determine if agents should be monitored"""
        return time.time() % 30 < 1  # Monitor every 30 seconds
    
    def _create_task_assignment_action(self) -> Action:
        """Create action for task assignment"""
        return Action(
            action_type="assign_tasks",
            parameters={"target_agents": self.managed_agents},
            timestamp=time.time(),
            expected_outcome="Distribute work among agents"
        )
    
    def _create_monitoring_action(self) -> Action:
        """Create action for monitoring agents"""
        return Action(
            action_type="monitor_agents",
            parameters={"agents": self.managed_agents},
            timestamp=time.time(),
            expected_outcome="Check agent status"
        )

    def get_status(self) -> Dict[str, Any]:
        """Get current coordinator status"""
        return {
            "agent_type": "CoordinatorAgent",
            "name": self.name,
            "state": self.state.value,
            "managed_agents": len(self.managed_agents),
            "active_tasks": len(self.task_assignments),
            "agent_status_updates": len(self.agent_status),
            "performance": self.performance_metrics
        }

class CollaborativeAgent(Agent):
    """
    An agent that can collaborate with other agents through communication.
    """
    
    def __init__(self, agent_id: str, name: str, communication_protocol: CommunicationProtocol):
        super().__init__(agent_id, name)
        self.communication = communication_protocol
        self.coordinator_id: Optional[str] = None
        self.peer_agents: List[str] = []
        self.shared_knowledge: Dict[str, Any] = {}
        self.current_task: Optional[Dict[str, Any]] = None
        
    def set_coordinator(self, coordinator_id: str) -> None:
        """Set the coordinator for this agent"""
        self.coordinator_id = coordinator_id
        
    def add_peer_agent(self, agent_id: str) -> None:
        """Add a peer agent for collaboration"""
        self.peer_agents.append(agent_id)
        
    def perceive(self, environment: Environment) -> Perception:
        """Perceive environment and process messages"""
        env_state = environment.get_state()
        
        # Process incoming messages
        messages = self.communication.get_messages_for_agent(self.agent_id)
        
        perception_data = {
            "environment": env_state,
            "messages": messages,
            "shared_knowledge_size": len(self.shared_knowledge),
            "has_task": self.current_task is not None
        }
        
        # Process messages and update knowledge
        for message in messages:
            self._process_message(message)
        
        return Perception(
            timestamp=time.time(),
            data=perception_data,
            source="collaborative_sensors",
            confidence=0.9
        )
    
    def decide(self, perception: Perception) -> Optional[Action]:
        """Decide action considering collaboration opportunities"""
        # If we have a task, work on it
        if self.current_task:
            return self._work_on_current_task()
        
        # If no task, request one from coordinator
        if self.coordinator_id:
            self._request_task_from_coordinator()
        
        # Share knowledge with peers occasionally
        if random.random() < 0.1 and self.peer_agents:
            return self._create_knowledge_sharing_action()
        
        return Action(
            action_type="wait",
            parameters={"reason": "awaiting_task_or_collaboration"},
            timestamp=time.time()
        )
    
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """Learn and share knowledge with other agents"""
        success = result.get("success", False)
        
        # Update personal knowledge
        if success and "new_knowledge" in result:
            self.shared_knowledge.update(result["new_knowledge"])
        
        # Share important discoveries with peers
        if success and result.get("important_discovery", False):
            self._share_discovery_with_peers(result)
        
        # Update task status
        if self.current_task and result.get("task_completed", False):
            self._report_task_completion()
            self.current_task = None
        
        logger.info(f"CollaborativeAgent {self.name} learned from {action.action_type}")
    
    def _process_message(self, message: Message) -> None:
        """Process incoming message"""
        if message.message_type == MessageType.RESPONSE:
            content = message.content
            if "task_assignment" in content:
                self.current_task = content["task_assignment"]
                logger.info(f"Agent {self.name} received task: {self.current_task['description']}")
        
        elif message.message_type == MessageType.INFORMATION:
            # Update shared knowledge
            if "knowledge" in message.content:
                self.shared_knowledge.update(message.content["knowledge"])
                logger.info(f"Agent {self.name} received knowledge from {message.sender_id}")
    
    def _work_on_current_task(self) -> Action:
        """Work on the currently assigned task"""
        task = self.current_task
        return Action(
            action_type="work_on_task",
            parameters={
                "task_id": task["task_id"],
                "task_description": task["description"],
                "progress": "in_progress"
            },
            timestamp=time.time(),
            expected_outcome="Make progress on assigned task"
        )
    
    def _request_task_from_coordinator(self) -> None:
        """Request a new task from the coordinator"""
        if not self.coordinator_id:
            return
        
        request = Message(
            sender_id=self.agent_id,
            receiver_id=self.coordinator_id,
            message_type=MessageType.REQUEST,
            content={
                "request_type": "task_assignment",
                "agent_capabilities": ["general_work", "collaboration"],
                "current_load": 0
            },
            timestamp=time.time()
        )
        
        self.communication.send_message(request)
    
    def _create_knowledge_sharing_action(self) -> Action:
        """Create action to share knowledge with peers"""
        return Action(
            action_type="share_knowledge",
            parameters={
                "target_agents": self.peer_agents,
                "knowledge_items": len(self.shared_knowledge)
            },
            timestamp=time.time(),
            expected_outcome="Improve collective intelligence"
        )
    
    def _share_discovery_with_peers(self, discovery: Dict[str, Any]) -> None:
        """Share important discovery with peer agents"""
        if not self.peer_agents:
            return
        
        self.communication.broadcast_message(
            sender_id=self.agent_id,
            message_type=MessageType.INFORMATION,
            content={"knowledge": discovery, "discovery_type": "important"},
            agent_ids=self.peer_agents
        )
    
    def _report_task_completion(self) -> None:
        """Report task completion to coordinator"""
        if not self.coordinator_id or not self.current_task:
            return
        
        report = Message(
            sender_id=self.agent_id,
            receiver_id=self.coordinator_id,
            message_type=MessageType.INFORMATION,
            content={
                "task_completed": True,
                "task_id": self.current_task["task_id"],
                "completion_time": time.time(),
                "status": "completed_successfully"
            },
            timestamp=time.time()
        )
        
        self.communication.send_message(report)
        logger.info(f"Agent {self.name} reported task completion: {self.current_task['task_id']}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "CollaborativeAgent",
            "name": self.name,
            "state": self.state.value,
            "has_coordinator": self.coordinator_id is not None,
            "peer_agents": len(self.peer_agents),
            "shared_knowledge_items": len(self.shared_knowledge),
            "current_task": self.current_task["task_id"] if self.current_task else None
        }
