"""
Learning Agent Implementation
This agent improves its performance through experience using reinforcement learning.
"""

import time
import random
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from collections import defaultdict, deque
from agent_base import Agent, Perception, Action, Environment, logger

class QLearningAgent(Agent):
    """
    A learning agent that uses Q-Learning to improve its decision-making.
    Demonstrates how agents can adapt and improve over time.
    """
    
    def __init__(self, agent_id: str, name: str, learning_rate: float = 0.1, 
                 discount_factor: float = 0.9, epsilon: float = 0.1):
        super().__init__(agent_id, name)
        
        # Q-Learning parameters
        self.learning_rate = learning_rate  # Alpha
        self.discount_factor = discount_factor  # Gamma
        self.epsilon = epsilon  # Exploration rate
        
        # Q-table: state -> action -> Q-value
        self.q_table: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        
        # Experience replay buffer
        self.experience_buffer = deque(maxlen=1000)
        
        # Learning statistics
        self.learning_stats = {
            "episodes": 0,
            "total_reward": 0.0,
            "average_reward": 0.0,
            "exploration_rate": epsilon
        }
        
        # Available actions
        self.action_space = [
            "move_north", "move_south", "move_east", "move_west",
            "pickup", "drop", "scan", "wait", "explore"
        ]
        
    def perceive(self, environment: Environment) -> Perception:
        """Perceive environment and extract state features"""
        env_state = environment.get_state()
        
        # Convert environment state to a state representation for Q-learning
        state_key = self._state_to_key(env_state)
        
        perception = Perception(
            timestamp=time.time(),
            data={"env_state": env_state, "state_key": state_key},
            source="environment_sensors",
            confidence=0.95
        )
        
        logger.info(f"LearningAgent {self.name} perceived state: {state_key}")
        return perception
    
    def decide(self, perception: Perception) -> Optional[Action]:
        """
        Decide action using epsilon-greedy Q-learning policy.
        Balance between exploration and exploitation.
        """
        state_key = perception.data["state_key"]
        
        # Epsilon-greedy action selection
        if random.random() < self.epsilon:
            # Explore: choose random action
            action_type = random.choice(self.action_space)
            logger.info(f"LearningAgent {self.name} exploring with action: {action_type}")
        else:
            # Exploit: choose best known action
            action_type = self._get_best_action(state_key)
            logger.info(f"LearningAgent {self.name} exploiting with action: {action_type}")
        
        action = Action(
            action_type=action_type,
            parameters={"state": state_key, "q_value": self.q_table[state_key][action_type]},
            timestamp=time.time(),
            expected_outcome="Learn from experience"
        )
        
        return action
    
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """
        Learn from experience using Q-learning update rule.
        Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
        """
        # Extract information from action and result
        state = action.parameters.get("state", "unknown")
        action_type = action.action_type
        reward = self._calculate_reward(result)
        next_state = self._state_to_key(result.get("new_state", {}))
        
        # Store experience for replay
        experience = (state, action_type, reward, next_state, result.get("done", False))
        self.experience_buffer.append(experience)
        
        # Q-learning update
        current_q = self.q_table[state][action_type]
        next_max_q = max(self.q_table[next_state].values()) if self.q_table[next_state] else 0.0
        
        # Q-learning formula
        new_q = current_q + self.learning_rate * (
            reward + self.discount_factor * next_max_q - current_q
        )
        
        self.q_table[state][action_type] = new_q
        
        # Update learning statistics
        self._update_learning_stats(reward)
        
        # Decay exploration rate over time
        self.epsilon = max(0.01, self.epsilon * 0.995)
        self.learning_stats["exploration_rate"] = self.epsilon
        
        # Perform experience replay occasionally
        if len(self.experience_buffer) > 50 and random.random() < 0.1:
            self._experience_replay()
        
        logger.info(f"LearningAgent {self.name} updated Q({state}, {action_type}): {current_q:.3f} -> {new_q:.3f}")
    
    def _state_to_key(self, state: Dict[str, Any]) -> str:
        """Convert environment state to a string key for Q-table"""
        # Simplified state representation
        key_parts = []
        
        # Position
        pos = state.get("position", {"x": 0, "y": 0})
        key_parts.append(f"pos_{pos.get('x', 0)}_{pos.get('y', 0)}")
        
        # Battery level (discretized)
        battery = state.get("battery_level", 100)
        battery_level = "high" if battery > 70 else "medium" if battery > 30 else "low"
        key_parts.append(f"battery_{battery_level}")
        
        # Items nearby
        items = state.get("nearby_items", [])
        key_parts.append(f"items_{len(items)}")
        
        # Obstacles
        obstacles = state.get("obstacles", [])
        key_parts.append(f"obstacles_{len(obstacles)}")
        
        return "|".join(key_parts)
    
    def _get_best_action(self, state_key: str) -> str:
        """Get the action with highest Q-value for given state"""
        if state_key not in self.q_table or not self.q_table[state_key]:
            return random.choice(self.action_space)
        
        # Return action with highest Q-value
        best_action = max(self.q_table[state_key].items(), key=lambda x: x[1])[0]
        return best_action
    
    def _calculate_reward(self, result: Dict[str, Any]) -> float:
        """Calculate reward based on action result"""
        reward = 0.0
        
        # Basic success/failure reward
        if result.get("success", False):
            reward += 1.0
        else:
            reward -= 0.5
        
        # Goal completion bonus
        if result.get("goal_completed", False):
            reward += 10.0
        
        # Efficiency bonus (negative reward for time)
        reward -= 0.1  # Small time penalty
        
        # Battery management
        battery_level = result.get("battery_level", 100)
        if battery_level < 20:
            reward -= 2.0  # Penalty for low battery
        
        # Exploration bonus
        if result.get("new_area_discovered", False):
            reward += 2.0
        
        return reward
    
    def _experience_replay(self, batch_size: int = 10) -> None:
        """Perform experience replay to improve learning"""
        if len(self.experience_buffer) < batch_size:
            return
        
        # Sample random batch of experiences
        batch = random.sample(list(self.experience_buffer), batch_size)
        
        for state, action, reward, next_state, done in batch:
            current_q = self.q_table[state][action]
            
            if done:
                target_q = reward
            else:
                next_max_q = max(self.q_table[next_state].values()) if self.q_table[next_state] else 0.0
                target_q = reward + self.discount_factor * next_max_q
            
            # Update Q-value
            self.q_table[state][action] = current_q + self.learning_rate * (target_q - current_q)
        
        logger.info(f"LearningAgent {self.name} performed experience replay with {batch_size} samples")
    
    def _update_learning_stats(self, reward: float) -> None:
        """Update learning statistics"""
        self.learning_stats["total_reward"] += reward
        self.learning_stats["episodes"] += 1
        
        if self.learning_stats["episodes"] > 0:
            self.learning_stats["average_reward"] = (
                self.learning_stats["total_reward"] / self.learning_stats["episodes"]
            )
    
    def get_q_table_summary(self) -> Dict[str, Any]:
        """Get summary of Q-table for analysis"""
        if not self.q_table:
            return {"states": 0, "total_q_values": 0}
        
        total_q_values = sum(len(actions) for actions in self.q_table.values())
        avg_q_value = np.mean([
            q_val for actions in self.q_table.values() 
            for q_val in actions.values()
        ]) if total_q_values > 0 else 0.0
        
        return {
            "states": len(self.q_table),
            "total_q_values": total_q_values,
            "average_q_value": avg_q_value,
            "max_q_value": max([
                max(actions.values()) for actions in self.q_table.values()
                if actions
            ], default=0.0),
            "min_q_value": min([
                min(actions.values()) for actions in self.q_table.values()
                if actions
            ], default=0.0)
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "QLearningAgent",
            "name": self.name,
            "state": self.state.value,
            "learning_stats": self.learning_stats,
            "q_table_summary": self.get_q_table_summary(),
            "experience_buffer_size": len(self.experience_buffer),
            "epsilon": self.epsilon
        }
