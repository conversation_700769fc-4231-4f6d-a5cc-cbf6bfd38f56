"""
Reactive Agent Implementation
This agent responds directly to environmental stimuli without internal state.
"""

import random
import time
from typing import Dict, Any, Optional
from agent_base import Agent, Perception, Action, Environment, logger

class ReactiveAgent(Agent):
    """
    A simple reactive agent that responds to stimuli based on predefined rules.
    This represents the simplest form of agentic AI.
    """
    
    def __init__(self, agent_id: str, name: str, reaction_rules: Dict[str, str] = None):
        super().__init__(agent_id, name)
        # Reaction rules map environmental conditions to actions
        self.reaction_rules = reaction_rules or {
            "temperature_high": "turn_on_cooling",
            "temperature_low": "turn_on_heating", 
            "motion_detected": "turn_on_lights",
            "no_motion": "turn_off_lights",
            "low_battery": "find_charging_station",
            "obstacle_detected": "avoid_obstacle"
        }
        
    def perceive(self, environment: Environment) -> Perception:
        """Perceive current environment state"""
        env_state = environment.get_state()
        
        # Simple perception - just capture current state
        perception = Perception(
            timestamp=time.time(),
            data=env_state,
            source="environment_sensors",
            confidence=0.9
        )
        
        logger.info(f"ReactiveAgent {self.name} perceived: {env_state}")
        return perception
    
    def decide(self, perception: Perception) -> Optional[Action]:
        """
        Make decision based on simple stimulus-response rules.
        No planning or goal consideration - pure reactive behavior.
        """
        env_data = perception.data
        
        # Check each condition in our reaction rules
        for condition, action_type in self.reaction_rules.items():
            if self._check_condition(condition, env_data):
                action = Action(
                    action_type=action_type,
                    parameters={"trigger": condition, "confidence": perception.confidence},
                    timestamp=time.time(),
                    expected_outcome=f"Respond to {condition}"
                )
                logger.info(f"ReactiveAgent {self.name} decided to {action_type} due to {condition}")
                return action
        
        # No matching condition found - random exploration
        available_actions = ["explore", "wait", "scan_environment"]
        action_type = random.choice(available_actions)
        
        return Action(
            action_type=action_type,
            parameters={"reason": "no_specific_trigger"},
            timestamp=time.time(),
            expected_outcome="Maintain activity"
        )
    
    def learn(self, action: Action, result: Dict[str, Any]) -> None:
        """
        Reactive agents typically don't learn, but we can track performance.
        """
        success = result.get("success", False)
        
        # Update simple performance metrics
        if "success_rate" not in self.performance_metrics:
            self.performance_metrics["success_rate"] = 0.0
            self.performance_metrics["total_actions"] = 0
        
        self.performance_metrics["total_actions"] += 1
        if success:
            self.performance_metrics["success_rate"] = (
                (self.performance_metrics["success_rate"] * (self.performance_metrics["total_actions"] - 1) + 1.0) /
                self.performance_metrics["total_actions"]
            )
        else:
            self.performance_metrics["success_rate"] = (
                (self.performance_metrics["success_rate"] * (self.performance_metrics["total_actions"] - 1)) /
                self.performance_metrics["total_actions"]
            )
        
        logger.info(f"ReactiveAgent {self.name} performance: {self.performance_metrics['success_rate']:.2f}")
    
    def _check_condition(self, condition: str, env_data: Dict[str, Any]) -> bool:
        """Check if a specific condition is met in the environment data"""
        if condition == "temperature_high":
            return env_data.get("temperature", 20) > 25
        elif condition == "temperature_low":
            return env_data.get("temperature", 20) < 18
        elif condition == "motion_detected":
            return env_data.get("motion", False)
        elif condition == "no_motion":
            return not env_data.get("motion", False)
        elif condition == "low_battery":
            return env_data.get("battery_level", 100) < 20
        elif condition == "obstacle_detected":
            return env_data.get("obstacle_distance", 100) < 10
        
        return False
    
    def add_reaction_rule(self, condition: str, action: str) -> None:
        """Add a new reaction rule to the agent"""
        self.reaction_rules[condition] = action
        logger.info(f"Added reaction rule: {condition} -> {action}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_type": "ReactiveAgent",
            "name": self.name,
            "state": self.state.value,
            "reaction_rules": len(self.reaction_rules),
            "performance": self.performance_metrics,
            "memory_size": len(self.memory)
        }
