# Agentic AI System - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Agentic AI system across different environments, from development to production.

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Development Deployment](#development-deployment)
5. [Production Deployment](#production-deployment)
6. [Docker Deployment](#docker-deployment)
7. [Cloud Deployment](#cloud-deployment)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Troubleshooting](#troubleshooting)

## System Requirements

### Minimum Requirements
```yaml
Hardware:
  CPU: 2 cores, 2.0 GHz
  RAM: 4 GB
  Storage: 1 GB available space
  Network: 100 Mbps (for distributed deployments)

Software:
  OS: Windows 10+, macOS 10.14+, Ubuntu 18.04+
  Python: 3.8+
  pip: 21.0+
```

### Recommended Requirements
```yaml
Hardware:
  CPU: 4+ cores, 3.0 GHz
  RAM: 8+ GB
  Storage: 10+ GB SSD
  Network: 1 Gbps
  GPU: Optional (for future ML extensions)

Software:
  OS: Ubuntu 20.04 LTS or Windows 11
  Python: 3.10+
  pip: 22.0+
```

### Production Requirements
```yaml
Hardware:
  CPU: 8+ cores, 3.5 GHz
  RAM: 16+ GB
  Storage: 50+ GB SSD
  Network: 10 Gbps
  Load Balancer: Required for multi-instance

Software:
  OS: Ubuntu 22.04 LTS
  Python: 3.11+
  Container Runtime: Docker 20.10+
  Orchestration: Kubernetes 1.24+ (optional)
```

## Installation

### 1. Environment Setup

#### Create Virtual Environment
```bash
# Using venv
python -m venv agentic_ai_env

# Activate environment
# Linux/macOS:
source agentic_ai_env/bin/activate
# Windows:
agentic_ai_env\Scripts\activate

# Verify activation
which python  # Should point to virtual environment
```

#### Alternative: Using Conda
```bash
# Create conda environment
conda create -n agentic_ai python=3.10
conda activate agentic_ai

# Verify environment
conda info --envs
```

### 2. Install Dependencies

#### Core Dependencies
```bash
# Install from requirements.txt
pip install -r requirements.txt

# Or install individually
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install matplotlib==3.7.2
pip install seaborn==0.12.2
pip install scikit-learn==1.3.0
```

#### Development Dependencies
```bash
# Additional development tools
pip install pytest==7.4.0
pip install black==23.3.0
pip install flake8==6.0.0
pip install mypy==1.4.1
pip install jupyter==1.0.0
```

#### Production Dependencies
```bash
# Production monitoring and logging
pip install prometheus-client==0.17.1
pip install structlog==23.1.0
pip install gunicorn==21.2.0  # If adding web interface
```

### 3. Verify Installation

#### Run Tests
```bash
# Run basic functionality tests
python test_agents.py

# Expected output: All tests should pass
# Tests Passed: 5/5
# 🎉 All tests passed! The Agentic AI system is working correctly.
```

#### Quick Simulation Test
```bash
# Run short simulation
python -c "
from simulation import AgenticAISimulation
sim = AgenticAISimulation(grid_size=5)
results = sim.run_simulation(steps=10)
print('Installation verified successfully!')
"
```

## Configuration

### 1. Configuration Files

#### Create `config.py`
```python
"""Configuration settings for Agentic AI system."""

import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class SimulationConfig:
    """Simulation configuration parameters."""
    grid_size: int = 10
    max_agents: int = 20
    simulation_steps: int = 1000
    step_delay: float = 0.0  # Delay between steps (seconds)
    enable_logging: bool = True
    log_level: str = "INFO"
    output_directory: str = "output"

@dataclass
class AgentConfig:
    """Agent configuration parameters."""
    # Learning agent parameters
    learning_rate: float = 0.1
    discount_factor: float = 0.9
    exploration_rate: float = 0.1
    experience_buffer_size: int = 1000
    
    # Goal-based agent parameters
    planning_timeout: float = 5.0  # seconds
    max_plan_length: int = 20
    replanning_threshold: float = 0.3
    
    # Reactive agent parameters
    reaction_timeout: float = 0.1  # seconds
    max_rules: int = 100

@dataclass
class EnvironmentConfig:
    """Environment configuration parameters."""
    obstacle_density: float = 0.15  # Percentage of grid cells
    item_density: float = 0.10
    battery_drain_rate: float = 1.0  # Per action
    perception_radius: int = 2
    max_inventory_size: int = 5

@dataclass
class CommunicationConfig:
    """Communication configuration parameters."""
    message_queue_size: int = 1000
    message_ttl: float = 60.0  # seconds
    broadcast_limit: int = 100  # Max recipients per broadcast
    enable_message_history: bool = True

# Global configuration instance
CONFIG = {
    'simulation': SimulationConfig(),
    'agents': AgentConfig(),
    'environment': EnvironmentConfig(),
    'communication': CommunicationConfig()
}

def load_config_from_file(filepath: str) -> None:
    """Load configuration from JSON file."""
    import json
    
    if os.path.exists(filepath):
        with open(filepath, 'r') as f:
            config_data = json.load(f)
        
        # Update configuration objects
        for section, values in config_data.items():
            if section in CONFIG:
                for key, value in values.items():
                    if hasattr(CONFIG[section], key):
                        setattr(CONFIG[section], key, value)

def save_config_to_file(filepath: str) -> None:
    """Save current configuration to JSON file."""
    import json
    from dataclasses import asdict
    
    config_data = {
        section: asdict(config_obj) 
        for section, config_obj in CONFIG.items()
    }
    
    with open(filepath, 'w') as f:
        json.dump(config_data, f, indent=2)
```

#### Create `config.json` (Optional)
```json
{
  "simulation": {
    "grid_size": 12,
    "max_agents": 25,
    "simulation_steps": 500,
    "log_level": "INFO"
  },
  "agents": {
    "learning_rate": 0.15,
    "exploration_rate": 0.2
  },
  "environment": {
    "obstacle_density": 0.12,
    "item_density": 0.08
  }
}
```

### 2. Environment Variables

#### Create `.env` file
```bash
# Agentic AI Environment Configuration

# Simulation settings
AGENTIC_AI_GRID_SIZE=10
AGENTIC_AI_MAX_AGENTS=20
AGENTIC_AI_LOG_LEVEL=INFO

# Performance settings
AGENTIC_AI_ENABLE_PROFILING=false
AGENTIC_AI_MEMORY_LIMIT=1000000000  # 1GB in bytes

# Output settings
AGENTIC_AI_OUTPUT_DIR=./output
AGENTIC_AI_SAVE_RESULTS=true

# Development settings
AGENTIC_AI_DEBUG_MODE=false
AGENTIC_AI_ENABLE_VISUALIZATION=false
```

#### Load Environment Variables
```python
# In your main application
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Use environment variables
GRID_SIZE = int(os.getenv('AGENTIC_AI_GRID_SIZE', 10))
LOG_LEVEL = os.getenv('AGENTIC_AI_LOG_LEVEL', 'INFO')
```

## Development Deployment

### 1. Development Setup

#### Project Structure
```
agentic-ai/
├── src/
│   ├── agent_base.py
│   ├── reactive_agent.py
│   ├── goal_based_agent.py
│   ├── learning_agent.py
│   ├── multi_agent_system.py
│   ├── environment.py
│   └── simulation.py
├── tests/
│   ├── test_agents.py
│   ├── test_environment.py
│   └── test_integration.py
├── config/
│   ├── config.py
│   ├── config.json
│   └── .env
├── output/
├── docs/
├── requirements.txt
├── requirements-dev.txt
└── README.md
```

#### Development Scripts

##### `run_dev.py`
```python
#!/usr/bin/env python3
"""Development runner script."""

import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from config.config import CONFIG, load_config_from_file
from simulation import AgenticAISimulation

def setup_logging():
    """Setup development logging."""
    logging.basicConfig(
        level=getattr(logging, CONFIG['simulation'].log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('dev.log')
        ]
    )

def main():
    """Run development simulation."""
    # Load configuration
    load_config_from_file('config/config.json')
    
    # Setup logging
    setup_logging()
    
    # Create and run simulation
    simulation = AgenticAISimulation(
        grid_size=CONFIG['simulation'].grid_size
    )
    
    results = simulation.run_simulation(
        steps=CONFIG['simulation'].simulation_steps
    )
    
    # Save results
    import json
    with open('output/dev_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print("Development simulation completed!")
    print(f"Results saved to output/dev_results.json")

if __name__ == "__main__":
    main()
```

### 2. Development Tools

#### Code Quality Tools
```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/

# Run all quality checks
./scripts/quality_check.sh
```

#### Testing
```bash
# Run all tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=src --cov-report=html

# Run specific test
pytest tests/test_agents.py::test_reactive_agent -v
```

## Production Deployment

### 1. Production Configuration

#### Production `config.json`
```json
{
  "simulation": {
    "grid_size": 20,
    "max_agents": 100,
    "simulation_steps": 10000,
    "log_level": "WARNING",
    "enable_logging": true
  },
  "agents": {
    "learning_rate": 0.05,
    "exploration_rate": 0.05,
    "experience_buffer_size": 5000
  },
  "environment": {
    "obstacle_density": 0.10,
    "item_density": 0.15
  }
}
```

#### Production Environment Variables
```bash
# Production .env
AGENTIC_AI_ENVIRONMENT=production
AGENTIC_AI_LOG_LEVEL=WARNING
AGENTIC_AI_MEMORY_LIMIT=8000000000  # 8GB
AGENTIC_AI_ENABLE_PROFILING=true
AGENTIC_AI_METRICS_ENABLED=true
AGENTIC_AI_METRICS_PORT=9090
```

### 2. Production Scripts

#### `run_production.py`
```python
#!/usr/bin/env python3
"""Production runner with monitoring and error handling."""

import sys
import logging
import signal
import time
from pathlib import Path
from typing import Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from config.config import CONFIG, load_config_from_file
from simulation import AgenticAISimulation

class ProductionRunner:
    """Production simulation runner with monitoring."""
    
    def __init__(self):
        self.simulation: Optional[AgenticAISimulation] = None
        self.running = False
        self.setup_signal_handlers()
        self.setup_logging()
    
    def setup_signal_handlers(self):
        """Setup graceful shutdown handlers."""
        signal.signal(signal.SIGINT, self.shutdown_handler)
        signal.signal(signal.SIGTERM, self.shutdown_handler)
    
    def shutdown_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logging.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def setup_logging(self):
        """Setup production logging."""
        logging.basicConfig(
            level=getattr(logging, CONFIG['simulation'].log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('production.log'),
                logging.handlers.RotatingFileHandler(
                    'production.log', maxBytes=10*1024*1024, backupCount=5
                )
            ]
        )
    
    def run(self):
        """Run production simulation with monitoring."""
        try:
            # Load configuration
            load_config_from_file('config/production_config.json')
            
            # Create simulation
            self.simulation = AgenticAISimulation(
                grid_size=CONFIG['simulation'].grid_size
            )
            
            self.running = True
            logging.info("Starting production simulation...")
            
            # Run simulation
            results = self.simulation.run_simulation(
                steps=CONFIG['simulation'].simulation_steps
            )
            
            # Save results with timestamp
            timestamp = int(time.time())
            results_file = f'output/production_results_{timestamp}.json'
            
            import json
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logging.info(f"Production simulation completed successfully")
            logging.info(f"Results saved to {results_file}")
            
        except Exception as e:
            logging.error(f"Production simulation failed: {e}", exc_info=True)
            sys.exit(1)

def main():
    """Main production entry point."""
    runner = ProductionRunner()
    runner.run()

if __name__ == "__main__":
    main()
```

### 3. Process Management

#### Systemd Service (Linux)
```ini
# /etc/systemd/system/agentic-ai.service
[Unit]
Description=Agentic AI Simulation Service
After=network.target

[Service]
Type=simple
User=agentic-ai
Group=agentic-ai
WorkingDirectory=/opt/agentic-ai
Environment=PATH=/opt/agentic-ai/venv/bin
ExecStart=/opt/agentic-ai/venv/bin/python run_production.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### Start/Stop Commands
```bash
# Enable and start service
sudo systemctl enable agentic-ai
sudo systemctl start agentic-ai

# Check status
sudo systemctl status agentic-ai

# View logs
sudo journalctl -u agentic-ai -f

# Stop service
sudo systemctl stop agentic-ai
```

## Docker Deployment

### 1. Dockerfile

#### Multi-stage Dockerfile
```dockerfile
# Build stage
FROM python:3.11-slim as builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

WORKDIR /app

# Create non-root user
RUN groupadd -r agentic && useradd -r -g agentic agentic

# Copy installed packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY run_production.py .

# Create output directory
RUN mkdir -p output && chown -R agentic:agentic /app

# Switch to non-root user
USER agentic

# Set environment variables
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app/src

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.path.insert(0, 'src'); from simulation import AgenticAISimulation; print('OK')" || exit 1

# Run application
CMD ["python", "run_production.py"]
```

### 2. Docker Compose

#### `docker-compose.yml`
```yaml
version: '3.8'

services:
  agentic-ai:
    build: .
    container_name: agentic-ai-simulation
    restart: unless-stopped
    environment:
      - AGENTIC_AI_ENVIRONMENT=production
      - AGENTIC_AI_LOG_LEVEL=INFO
    volumes:
      - ./output:/app/output
      - ./config:/app/config:ro
      - ./logs:/app/logs
    networks:
      - agentic-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  monitoring:
    image: prom/prometheus:latest
    container_name: agentic-ai-monitoring
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - agentic-network

networks:
  agentic-network:
    driver: bridge
```

### 3. Docker Commands

#### Build and Run
```bash
# Build image
docker build -t agentic-ai:latest .

# Run container
docker run -d \
  --name agentic-ai \
  --restart unless-stopped \
  -v $(pwd)/output:/app/output \
  -v $(pwd)/config:/app/config:ro \
  agentic-ai:latest

# Using docker-compose
docker-compose up -d

# View logs
docker logs -f agentic-ai

# Stop containers
docker-compose down
```

## Cloud Deployment

### 1. AWS Deployment

#### EC2 Instance Setup
```bash
# Launch EC2 instance (Ubuntu 22.04 LTS)
# Instance type: t3.medium or larger
# Security group: Allow SSH (22) and custom ports as needed

# Connect to instance
ssh -i your-key.pem ubuntu@your-instance-ip

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Deploy application
git clone https://github.com/your-repo/agentic-ai.git
cd agentic-ai
docker-compose up -d
```

#### ECS Deployment (Task Definition)
```json
{
  "family": "agentic-ai-task",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "agentic-ai",
      "image": "your-account.dkr.ecr.region.amazonaws.com/agentic-ai:latest",
      "essential": true,
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/agentic-ai",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "environment": [
        {
          "name": "AGENTIC_AI_ENVIRONMENT",
          "value": "production"
        }
      ]
    }
  ]
}
```

### 2. Google Cloud Platform

#### Cloud Run Deployment
```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/agentic-ai

# Deploy to Cloud Run
gcloud run deploy agentic-ai \
  --image gcr.io/PROJECT_ID/agentic-ai \
  --platform managed \
  --region us-central1 \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10
```

### 3. Azure Deployment

#### Container Instances
```bash
# Create resource group
az group create --name agentic-ai-rg --location eastus

# Create container instance
az container create \
  --resource-group agentic-ai-rg \
  --name agentic-ai-instance \
  --image your-registry/agentic-ai:latest \
  --cpu 2 \
  --memory 4 \
  --restart-policy Always
```

## Monitoring and Maintenance

### 1. Health Monitoring

#### Health Check Script
```python
#!/usr/bin/env python3
"""Health check script for Agentic AI system."""

import sys
import time
import psutil
import logging
from pathlib import Path

def check_system_resources():
    """Check system resource usage."""
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    issues = []
    
    if cpu_percent > 80:
        issues.append(f"High CPU usage: {cpu_percent}%")
    
    if memory.percent > 85:
        issues.append(f"High memory usage: {memory.percent}%")
    
    if disk.percent > 90:
        issues.append(f"High disk usage: {disk.percent}%")
    
    return issues

def check_application_health():
    """Check application-specific health."""
    try:
        # Import and test core components
        sys.path.insert(0, 'src')
        from simulation import AgenticAISimulation
        
        # Quick functionality test
        sim = AgenticAISimulation(grid_size=3)
        # Test would run here
        
        return []
    except Exception as e:
        return [f"Application health check failed: {e}"]

def main():
    """Run health checks."""
    all_issues = []
    
    # Check system resources
    all_issues.extend(check_system_resources())
    
    # Check application health
    all_issues.extend(check_application_health())
    
    if all_issues:
        print("HEALTH CHECK FAILED:")
        for issue in all_issues:
            print(f"  - {issue}")
        sys.exit(1)
    else:
        print("HEALTH CHECK PASSED")
        sys.exit(0)

if __name__ == "__main__":
    main()
```

### 2. Log Management

#### Log Rotation Configuration
```bash
# /etc/logrotate.d/agentic-ai
/opt/agentic-ai/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 agentic-ai agentic-ai
    postrotate
        systemctl reload agentic-ai
    endscript
}
```

### 3. Backup Strategy

#### Backup Script
```bash
#!/bin/bash
# backup.sh - Backup Agentic AI data

BACKUP_DIR="/backup/agentic-ai"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="agentic_ai_backup_${TIMESTAMP}.tar.gz"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create backup
tar -czf "$BACKUP_DIR/$BACKUP_FILE" \
    --exclude='*.log' \
    --exclude='__pycache__' \
    /opt/agentic-ai/

# Keep only last 7 backups
find "$BACKUP_DIR" -name "agentic_ai_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/$BACKUP_FILE"
```

## Troubleshooting

### Common Issues

#### 1. Memory Issues
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head

# Monitor memory in real-time
watch -n 1 'free -h && echo && ps aux --sort=-%mem | head -10'

# Solution: Increase memory limits or optimize Q-table storage
```

#### 2. Performance Issues
```bash
# Profile application
python -m cProfile -o profile.stats run_production.py

# Analyze profile
python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(20)
"

# Solution: Implement optimizations from performance analysis
```

#### 3. Communication Issues
```bash
# Check network connectivity
netstat -tulpn | grep python

# Monitor message queue
# Add monitoring to communication protocol

# Solution: Implement message batching and queue management
```

### Debug Mode

#### Enable Debug Logging
```python
# In config.py
CONFIG['simulation'].log_level = 'DEBUG'

# Or via environment variable
export AGENTIC_AI_LOG_LEVEL=DEBUG
```

#### Debug Runner
```python
#!/usr/bin/env python3
"""Debug runner with detailed logging."""

import logging
import sys
from pathlib import Path

# Setup debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug.log')
    ]
)

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from simulation import AgenticAISimulation

def main():
    """Run debug simulation."""
    logging.info("Starting debug simulation...")
    
    try:
        simulation = AgenticAISimulation(grid_size=5)
        results = simulation.run_simulation(steps=20)
        
        logging.info("Debug simulation completed successfully")
        print("Debug results:", results)
        
    except Exception as e:
        logging.error("Debug simulation failed", exc_info=True)
        raise

if __name__ == "__main__":
    main()
```

This deployment guide provides comprehensive instructions for deploying the Agentic AI system across different environments, ensuring reliable and scalable operation.
